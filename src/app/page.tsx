import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Gamepad2,
  Trophy,
  Shield,
  ArrowRight,
  Star
} from "lucide-react";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Navigation */}
      <nav className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Guildie</h1>
            <div className="flex items-center gap-4">
              <Button variant="ghost" asChild>
                <Link href="/profile/shadowhunter92">Demo Profile</Link>
              </Button>
              <Button variant="outline">Sign In</Button>
              <Button>Get Started</But<PERSON>>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <main className="container mx-auto px-4 py-16 max-w-6xl">
        <div className="text-center space-y-6 mb-16">
          <Badge variant="secondary" className="mb-4">
            <Star className="h-3 w-3 mr-1" />
            Connect with Gaming Communities
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold tracking-tight">
            Find Your Perfect
            <span className="text-primary"> Gaming Guild</span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Connect with fellow gamers, showcase your achievements, and discover guilds
            that match your playstyle across multiple games.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/profile/shadowhunter92">
                View Demo Profile
                <ArrowRight className="h-4 w-4 ml-2" />
              </Link>
            </Button>
            <Button variant="outline" size="lg">
              Learn More
            </Button>
          </div>
        </div>

        {/* Features */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 mb-16">
          <Card>
            <CardHeader>
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Connect with Gamers</CardTitle>
              <CardDescription>
                Build your gaming network and find players who share your interests and playstyle.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Shield className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Join Guilds</CardTitle>
              <CardDescription>
                Discover active guilds across multiple games and find your perfect gaming community.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card>
            <CardHeader>
              <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                <Trophy className="h-6 w-6 text-primary" />
              </div>
              <CardTitle>Showcase Achievements</CardTitle>
              <CardDescription>
                Display your gaming accomplishments and track your progress across all your favorite games.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* Demo Profile Preview */}
        <Card className="mb-16">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">See It In Action</CardTitle>
            <CardDescription>
              Check out a sample gamer profile to see what Guildie can do for you
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <div className="bg-muted/50 rounded-lg p-8 mb-6">
              <div className="flex items-center justify-center gap-4 mb-4">
                <div className="h-16 w-16 bg-primary/20 rounded-full flex items-center justify-center">
                  <Gamepad2 className="h-8 w-8 text-primary" />
                </div>
                <div className="text-left">
                  <h3 className="text-lg font-semibold">ShadowHunter</h3>
                  <p className="text-muted-foreground">Mythic Raider • 4,000+ Hours Played</p>
                </div>
              </div>
              <div className="flex justify-center gap-4 text-sm">
                <Badge variant="outline">World of Warcraft</Badge>
                <Badge variant="outline">Final Fantasy XIV</Badge>
                <Badge variant="outline">47 Achievements</Badge>
              </div>
            </div>
            <Button size="lg" asChild>
              <Link href="/profile/shadowhunter92">
                View Full Profile
                <ArrowRight className="h-4 w-4 ml-2" />
              </Link>
            </Button>
          </CardContent>
        </Card>
      </main>

      {/* Footer */}
      <footer className="border-t bg-muted/50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-muted-foreground">
            <p>&copy; 2024 Guildie. Built with Next.js, TypeScript, and shadcn/ui.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
