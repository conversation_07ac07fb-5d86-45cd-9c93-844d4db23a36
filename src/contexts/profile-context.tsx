'use client';

import { createContext, useContext, useReducer, ReactNode, useCallback, useMemo } from 'react';
import { GamerProfile, Achievement } from '@/types/gamer';

interface ProfileState {
  profile: GamerProfile | null;
  recentAchievements: Achievement[];
  loading: boolean;
  error: string | null;
  isEditing: boolean;
}

type ProfileAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_PROFILE'; payload: GamerProfile }
  | { type: 'SET_RECENT_ACHIEVEMENTS'; payload: Achievement[] }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_EDITING'; payload: boolean }
  | { type: 'UPDATE_PROFILE'; payload: Partial<GamerProfile> }
  | { type: 'ADD_ACHIEVEMENT'; payload: Achievement }
  | { type: 'RESET' };

const initialState: ProfileState = {
  profile: null,
  recentAchievements: [],
  loading: false,
  error: null,
  isEditing: false,
};

function profileReducer(state: ProfileState, action: ProfileAction): ProfileState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_PROFILE':
      return { ...state, profile: action.payload, error: null };
    
    case 'SET_RECENT_ACHIEVEMENTS':
      return { ...state, recentAchievements: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'SET_EDITING':
      return { ...state, isEditing: action.payload };
    
    case 'UPDATE_PROFILE':
      if (!state.profile) return state;
      return {
        ...state,
        profile: { ...state.profile, ...action.payload },
        isEditing: false,
      };
    
    case 'ADD_ACHIEVEMENT':
      return {
        ...state,
        recentAchievements: [action.payload, ...state.recentAchievements.slice(0, 9)],
        profile: state.profile ? {
          ...state.profile,
          achievements: [action.payload, ...state.profile.achievements],
          stats: {
            ...state.profile.stats,
            achievementsUnlocked: state.profile.stats.achievementsUnlocked + 1,
          },
        } : null,
      };
    
    case 'RESET':
      return initialState;
    
    default:
      return state;
  }
}

interface ProfileContextType {
  state: ProfileState;
  dispatch: React.Dispatch<ProfileAction>;
  actions: {
    setLoading: (loading: boolean) => void;
    setProfile: (profile: GamerProfile) => void;
    setRecentAchievements: (achievements: Achievement[]) => void;
    setError: (error: string | null) => void;
    setEditing: (editing: boolean) => void;
    updateProfile: (updates: Partial<GamerProfile>) => void;
    addAchievement: (achievement: Achievement) => void;
    reset: () => void;
  };
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export function ProfileProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(profileReducer, initialState);

  const actions = useMemo(() => ({
    setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading }),
    setProfile: (profile: GamerProfile) => dispatch({ type: 'SET_PROFILE', payload: profile }),
    setRecentAchievements: (achievements: Achievement[]) =>
      dispatch({ type: 'SET_RECENT_ACHIEVEMENTS', payload: achievements }),
    setError: (error: string | null) => dispatch({ type: 'SET_ERROR', payload: error }),
    setEditing: (editing: boolean) => dispatch({ type: 'SET_EDITING', payload: editing }),
    updateProfile: (updates: Partial<GamerProfile>) =>
      dispatch({ type: 'UPDATE_PROFILE', payload: updates }),
    addAchievement: (achievement: Achievement) =>
      dispatch({ type: 'ADD_ACHIEVEMENT', payload: achievement }),
    reset: () => dispatch({ type: 'RESET' }),
  }), [dispatch]);

  return (
    <ProfileContext.Provider value={{ state, dispatch, actions }}>
      {children}
    </ProfileContext.Provider>
  );
}

export function useProfile() {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
}

// Custom hooks for specific profile operations
export function useProfileActions() {
  const { actions } = useProfile();
  return actions;
}

export function useProfileState() {
  const { state } = useProfile();
  return state;
}
