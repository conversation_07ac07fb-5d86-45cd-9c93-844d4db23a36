import { GamerProfile, GameProfile, Character, Achievement } from '@/types/gamer';

export const mockGamerProfile: GamerProfile = {
  id: 'user-123',
  username: 'shadowhunter92',
  displayName: 'ShadowHunter',
  avatar: '/avatars/shadow-hunter.jpg',
  bio: 'Passionate MMORPG player with 10+ years of experience. Love raiding, PvP, and helping new players. Always looking for active guilds!',
  location: 'San Francisco, CA',
  timezone: 'PST',
  joinDate: new Date('2020-03-15'),
  isOnline: true,
  lastSeen: new Date(),
  
  playStyle: 'mixed',
  preferredRoles: ['Tank', 'DPS', 'Support'],
  availableHours: {
    monday: [{ start: '19:00', end: '23:00' }],
    tuesday: [{ start: '19:00', end: '23:00' }],
    wednesday: [{ start: '19:00', end: '23:00' }],
    thursday: [{ start: '19:00', end: '23:00' }],
    friday: [{ start: '18:00', end: '02:00' }],
    saturday: [{ start: '14:00', end: '02:00' }],
    sunday: [{ start: '14:00', end: '23:00' }],
  },
  
  games: [
    {
      gameId: 'wow',
      gameName: 'World of Warcraft',
      gameIcon: '/game-icons/wow.png',
      platform: 'battlenet',
      server: 'Stormrage',
      region: 'US',
      characters: [
        {
          id: 'char-1',
          name: 'Shadowmend',
          class: 'Death Knight',
          race: 'Human',
          level: 80,
          server: 'Stormrage',
          faction: 'Alliance',
          specialization: 'Blood',
          itemLevel: 485,
          isPrimary: true,
        },
        {
          id: 'char-2',
          name: 'Lightbringer',
          class: 'Paladin',
          race: 'Dwarf',
          level: 75,
          server: 'Stormrage',
          faction: 'Alliance',
          specialization: 'Protection',
          itemLevel: 450,
          isPrimary: false,
        },
      ],
      hoursPlayed: 2847,
      level: 80,
      rank: 'Mythic Raider',
      achievements: [
        {
          id: 'wow-ach-1',
          title: 'Cutting Edge: Fyrakk',
          description: 'Defeat Fyrakk the Blazing on Mythic difficulty',
          rarity: 'legendary',
          unlockedAt: new Date('2024-01-15'),
          gameSpecific: true,
          category: 'Raids',
        },
      ],
      startedPlaying: new Date('2019-08-20'),
      lastPlayed: new Date(),
    },
    {
      gameId: 'ff14',
      gameName: 'Final Fantasy XIV',
      gameIcon: '/game-icons/ff14.png',
      platform: 'steam',
      server: 'Gilgamesh',
      region: 'NA',
      characters: [
        {
          id: 'char-3',
          name: 'Kira Nightfall',
          class: 'Dark Knight',
          race: 'Au Ra',
          level: 90,
          server: 'Gilgamesh',
          specialization: 'Tank',
          itemLevel: 630,
          isPrimary: true,
        },
      ],
      hoursPlayed: 1250,
      level: 90,
      achievements: [],
      startedPlaying: new Date('2021-07-01'),
      lastPlayed: new Date('2024-12-10'),
    },
  ],
  
  achievements: [
    {
      id: 'global-ach-1',
      title: 'Guild Master',
      description: 'Successfully lead a guild for 6 months',
      rarity: 'rare',
      unlockedAt: new Date('2023-06-15'),
    },
    {
      id: 'global-ach-2',
      title: 'Veteran Player',
      description: 'Play for over 1000 hours across all games',
      rarity: 'uncommon',
      unlockedAt: new Date('2022-11-20'),
    },
  ],
  
  friends: ['user-456', 'user-789'],
  guilds: [
    {
      guildId: 'guild-1',
      guildName: 'Shadow Legends',
      guildTag: 'SHDW',
      role: 'officer',
      joinedAt: new Date('2023-01-10'),
      gameId: 'wow',
      isActive: true,
    },
    {
      guildId: 'guild-2',
      guildName: 'Crystal Guardians',
      role: 'member',
      joinedAt: new Date('2023-08-15'),
      gameId: 'ff14',
      isActive: true,
    },
  ],
  
  privacy: {
    profileVisibility: 'public',
    showOnlineStatus: true,
    showGameActivity: true,
    showAchievements: true,
    showStats: true,
    allowFriendRequests: true,
    allowGuildInvites: true,
  },
  
  stats: {
    totalHoursPlayed: 4097,
    gamesOwned: 2,
    achievementsUnlocked: 47,
    guildsJoined: 5,
    friendsCount: 23,
    favoriteGame: 'World of Warcraft',
    mostPlayedGenre: 'mmorpg',
    averageSessionLength: 180, // 3 hours
  },
};

export const mockRecentAchievements: Achievement[] = [
  {
    id: 'recent-1',
    title: 'Mythic+ Master',
    description: 'Complete 100 Mythic+ dungeons',
    rarity: 'epic',
    unlockedAt: new Date('2024-12-14'),
    gameId: 'wow',
  },
  {
    id: 'recent-2',
    title: 'Social Butterfly',
    description: 'Add 20 friends to your network',
    rarity: 'common',
    unlockedAt: new Date('2024-12-12'),
  },
  {
    id: 'recent-3',
    title: 'Raid Leader',
    description: 'Successfully lead 10 raid groups',
    rarity: 'rare',
    unlockedAt: new Date('2024-12-08'),
    gameId: 'wow',
  },
];

// Helper function to simulate API delay
export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API functions
export const fetchGamerProfile = async (userId: string): Promise<GamerProfile> => {
  await delay(1000); // Simulate network delay
  return mockGamerProfile;
};

export const updateGamerProfile = async (userId: string, updates: Partial<GamerProfile>): Promise<GamerProfile> => {
  await delay(800);
  return { ...mockGamerProfile, ...updates };
};

export const fetchRecentAchievements = async (userId: string): Promise<Achievement[]> => {
  await delay(600);
  return mockRecentAchievements;
};
