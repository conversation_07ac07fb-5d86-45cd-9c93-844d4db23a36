'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Achievement, GameAchievement } from '@/types/gamer';
import { 
  Trophy, 
  Star, 
  Crown, 
  Award,
  Calendar,
  Filter,
  Gamepad2
} from 'lucide-react';

interface AchievementsProps {
  achievements: Achievement[];
  recentAchievements?: Achievement[];
}

export function Achievements({ achievements, recentAchievements = [] }: AchievementsProps) {
  const [filter, setFilter] = useState<'all' | 'recent' | 'rare'>('all');

  const getRarityColor = (rarity: Achievement['rarity']) => {
    const colors = {
      common: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      uncommon: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      rare: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
      epic: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
      legendary: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    };
    return colors[rarity];
  };

  const getRarityIcon = (rarity: Achievement['rarity']) => {
    switch (rarity) {
      case 'legendary':
        return <Crown className="h-4 w-4" />;
      case 'epic':
        return <Award className="h-4 w-4" />;
      case 'rare':
        return <Star className="h-4 w-4" />;
      default:
        return <Trophy className="h-4 w-4" />;
    }
  };

  const getFilteredAchievements = () => {
    switch (filter) {
      case 'recent':
        return recentAchievements;
      case 'rare':
        return achievements.filter(ach => ['rare', 'epic', 'legendary'].includes(ach.rarity));
      default:
        return achievements;
    }
  };

  const AchievementCard = ({ achievement }: { achievement: Achievement }) => {
    const isGameSpecific = 'gameSpecific' in achievement && achievement.gameSpecific;
    const gameAchievement = achievement as GameAchievement;
    
    return (
      <Card className="transition-all hover:shadow-md">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className={`p-2 rounded-lg ${getRarityColor(achievement.rarity)}`}>
              {achievement.icon ? (
                <img src={achievement.icon} alt="" className="h-6 w-6" />
              ) : (
                getRarityIcon(achievement.rarity)
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-2">
                <div>
                  <h4 className="font-semibold text-sm leading-tight">
                    {achievement.title}
                  </h4>
                  <p className="text-xs text-muted-foreground mt-1 leading-relaxed">
                    {achievement.description}
                  </p>
                </div>
                
                <Badge 
                  className={`${getRarityColor(achievement.rarity)} text-xs shrink-0`}
                  variant="secondary"
                >
                  {achievement.rarity}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between mt-3">
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  <span>{achievement.unlockedAt.toLocaleDateString()}</span>
                  {isGameSpecific && gameAchievement.category && (
                    <>
                      <span>•</span>
                      <span>{gameAchievement.category}</span>
                    </>
                  )}
                </div>
                
                {achievement.gameId && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Gamepad2 className="h-3 w-3" />
                    <span>Game Achievement</span>
                  </div>
                )}
              </div>
              
              {achievement.progress !== undefined && achievement.maxProgress && (
                <div className="mt-2">
                  <div className="flex justify-between text-xs text-muted-foreground mb-1">
                    <span>Progress</span>
                    <span>{achievement.progress}/{achievement.maxProgress}</span>
                  </div>
                  <Progress 
                    value={(achievement.progress / achievement.maxProgress) * 100} 
                    className="h-1"
                  />
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const getRarityStats = () => {
    const stats = achievements.reduce((acc, ach) => {
      acc[ach.rarity] = (acc[ach.rarity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return stats;
  };

  const rarityStats = getRarityStats();
  const filteredAchievements = getFilteredAchievements();

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Achievements</h2>
        <div className="flex items-center gap-2">
          <Button
            variant={filter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('all')}
          >
            All ({achievements.length})
          </Button>
          <Button
            variant={filter === 'recent' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('recent')}
          >
            Recent ({recentAchievements.length})
          </Button>
          <Button
            variant={filter === 'rare' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('rare')}
          >
            <Filter className="h-4 w-4 mr-1" />
            Rare+
          </Button>
        </div>
      </div>

      {/* Achievement Stats */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        {Object.entries(rarityStats).map(([rarity, count]) => (
          <Card key={rarity}>
            <CardContent className="p-3 text-center">
              <div className={`inline-flex p-2 rounded-lg ${getRarityColor(rarity as Achievement['rarity'])}`}>
                {getRarityIcon(rarity as Achievement['rarity'])}
              </div>
              <div className="mt-2">
                <div className="text-lg font-bold">{count}</div>
                <div className="text-xs text-muted-foreground capitalize">{rarity}</div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Recent Achievements Highlight */}
      {recentAchievements.length > 0 && filter === 'all' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              Recent Achievements
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2">
              {recentAchievements.slice(0, 4).map((achievement) => (
                <div key={achievement.id} className="flex items-center gap-3 p-2 rounded-lg bg-muted/50">
                  <div className={`p-1.5 rounded ${getRarityColor(achievement.rarity)}`}>
                    {getRarityIcon(achievement.rarity)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-sm truncate">{achievement.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {achievement.unlockedAt.toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Achievement Grid */}
      <div className="grid gap-4 md:grid-cols-2">
        {filteredAchievements.map((achievement) => (
          <AchievementCard key={achievement.id} achievement={achievement} />
        ))}
      </div>

      {filteredAchievements.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <Trophy className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No achievements found</h3>
            <p className="text-muted-foreground">
              {filter === 'recent' 
                ? "No recent achievements to display."
                : filter === 'rare'
                ? "No rare achievements unlocked yet."
                : "Start playing games to unlock achievements!"
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
