'use client';

import { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { GamerProfile } from '@/types/gamer';
import {
  MapPin,
  Clock,
  Calendar,
  Edit3,
  Users,
  Trophy,
  Gamepad2,
  Shield,
  Sword,
  Heart,
  MessageCircle,
  UserPlus,
  ExternalLink
} from 'lucide-react';

interface ProfileHeaderProps {
  profile: GamerProfile;
  isOwnProfile?: boolean;
  onProfileUpdate?: (updates: Partial<GamerProfile>) => void;
}

export function ProfileHeader({ profile, isOwnProfile = false, onProfileUpdate }: ProfileHeaderProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    displayName: profile.displayName,
    bio: profile.bio || '',
    location: profile.location || '',
  });

  const handleSave = () => {
    onProfileUpdate?.(editForm);
    setIsEditing(false);
  };

  const getPlayStyleIcon = (playStyle: string) => {
    switch (playStyle) {
      case 'competitive':
        return <Sword className="h-4 w-4" />;
      case 'casual':
        return <Heart className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getPlayStyleColor = (playStyle: string) => {
    switch (playStyle) {
      case 'competitive':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'casual':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
    }
  };

  return (
    <Card className="w-full">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-6">
            {/* Avatar and Online Status */}
            <div className="flex flex-col items-center md:items-start">
              <div className="relative">
                <Avatar className="h-24 w-24 md:h-32 md:w-32 shadow-lg">
                  <AvatarImage src={profile.avatar} alt={profile.displayName} />
                  <AvatarFallback className="text-2xl">
                    {profile.displayName.slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                {profile.isOnline && (
                  <div
                    className="absolute -bottom-1 -right-1 h-6 w-6 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"
                    aria-label="User is online"
                    title="Online"
                  />
                )}
              </div>
              <div className="mt-2 text-center md:text-left">
                <Badge variant={profile.isOnline ? 'default' : 'secondary'} className="text-xs">
                  {profile.isOnline ? 'Online' : 'Offline'}
                </Badge>
              </div>
            </div>

          {/* Profile Information */}
          <div className="flex-1 space-y-4">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
              <div>
                {/* Small colorful player stats above name */}
                <div className="flex flex-wrap gap-1.5 mb-2">
                  <Badge className={`${getPlayStyleColor(profile.playStyle)} flex items-center gap-1 text-xs px-2 py-0.5`}>
                    {getPlayStyleIcon(profile.playStyle)}
                    {profile.playStyle.charAt(0).toUpperCase() + profile.playStyle.slice(1)}
                  </Badge>

                  <Badge variant="outline" className="flex items-center gap-1 text-xs px-2 py-0.5 border-blue-200 text-blue-700 bg-blue-50 dark:border-blue-800 dark:text-blue-300 dark:bg-blue-950">
                    <Gamepad2 className="h-3 w-3" />
                    {profile.stats.gamesOwned}
                  </Badge>

                  <Badge variant="outline" className="flex items-center gap-1 text-xs px-2 py-0.5 border-yellow-200 text-yellow-700 bg-yellow-50 dark:border-yellow-800 dark:text-yellow-300 dark:bg-yellow-950">
                    <Trophy className="h-3 w-3" />
                    {profile.stats.achievementsUnlocked}
                  </Badge>

                  <Badge variant="outline" className="flex items-center gap-1 text-xs px-2 py-0.5 border-green-200 text-green-700 bg-green-50 dark:border-green-800 dark:text-green-300 dark:bg-green-950">
                    <Users className="h-3 w-3" />
                    {profile.stats.friendsCount}
                  </Badge>
                </div>

                <h1 className="text-2xl md:text-3xl font-bold">{profile.displayName}</h1>
                <p className="text-muted-foreground">@{profile.username}</p>

                {/* Social Links */}
                <div className="flex items-center gap-3 mt-2">
                  {/* Discord Link - Mock data for now */}
                  <Button variant="ghost" size="sm" className="h-8 px-2 text-[#5865F2] hover:bg-[#5865F2]/10">
                    <svg className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515a.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0a12.64 12.64 0 0 0-.617-1.25a.077.077 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057a19.9 19.9 0 0 0 5.993 3.03a.078.078 0 0 0 .084-.028a14.09 14.09 0 0 0 1.226-1.994a.076.076 0 0 0-.041-.106a13.107 13.107 0 0 1-1.872-.892a.077.077 0 0 1-.008-.128a10.2 10.2 0 0 0 .372-.292a.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.**************.373.292a.077.077 0 0 1-.006.127a12.299 12.299 0 0 1-1.873.892a.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028a19.839 19.839 0 0 0 6.002-3.03a.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.956-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419c0-1.333.955-2.419 2.157-2.419c1.21 0 2.176 1.096 2.157 2.42c0 1.333-.946 2.418-2.157 2.418z"/>
                    </svg>
                    Discord
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Button>

                  {/* Twitch Link - Mock data for now */}
                  <Button variant="ghost" size="sm" className="h-8 px-2 text-[#9146FF] hover:bg-[#9146FF]/10">
                    <svg className="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M11.571 4.714h1.715v5.143H11.57zm4.715 0H18v5.143h-1.714zM6 0L1.714 4.286v15.428h5.143V24l4.286-4.286h3.428L22.286 12V0zm14.571 11.143l-3.428 3.428h-3.429l-3 3v-3H6.857V1.714h13.714Z"/>
                    </svg>
                    Twitch
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              </div>

              <div className="flex items-center gap-2">
                {!isOwnProfile && (
                  <>
                    <Button variant="outline" size="sm">
                      <UserPlus className="h-4 w-4 mr-2" />
                      Add Friend
                    </Button>
                    <Button variant="default" size="sm">
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Message
                    </Button>
                  </>
                )}

                {isOwnProfile && (
                <Dialog open={isEditing} onOpenChange={setIsEditing}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm" aria-label="Edit profile information">
                      <Edit3 className="h-4 w-4 mr-2" aria-hidden="true" />
                      Edit Profile
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                      <DialogTitle>Edit Profile</DialogTitle>
                    </DialogHeader>
                    <div className="grid gap-4 py-4">
                      <div className="grid gap-2">
                        <Label htmlFor="displayName">Display Name</Label>
                        <Input
                          id="displayName"
                          value={editForm.displayName}
                          onChange={(e) => setEditForm({ ...editForm, displayName: e.target.value })}
                          aria-describedby="displayName-description"
                        />
                        <p id="displayName-description" className="sr-only">
                          Enter your display name as it will appear to other users
                        </p>
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="bio">Bio</Label>
                        <Textarea
                          id="bio"
                          value={editForm.bio}
                          onChange={(e) => setEditForm({ ...editForm, bio: e.target.value })}
                          rows={3}
                          aria-describedby="bio-description"
                          maxLength={500}
                        />
                        <p id="bio-description" className="text-xs text-muted-foreground">
                          Tell other gamers about yourself (max 500 characters)
                        </p>
                      </div>
                      <div className="grid gap-2">
                        <Label htmlFor="location">Location</Label>
                        <Input
                          id="location"
                          value={editForm.location}
                          onChange={(e) => setEditForm({ ...editForm, location: e.target.value })}
                        />
                      </div>
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setIsEditing(false)}>
                        Cancel
                      </Button>
                      <Button onClick={handleSave}>Save Changes</Button>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </div>

            {/* Bio */}
            {profile.bio && (
              <p className="text-sm text-muted-foreground leading-relaxed">
                {profile.bio}
              </p>
            )}

            {/* Profile Details */}
            <ul className="space-y-1 text-sm text-muted-foreground">
              {profile.location && (
                <li className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 flex-shrink-0" />
                  <span>{profile.location}</span>
                </li>
              )}
              {profile.timezone && (
                <li className="flex items-center gap-2">
                  <Clock className="h-4 w-4 flex-shrink-0" />
                  <span>{profile.timezone}</span>
                </li>
              )}
              <li className="flex items-center gap-2">
                <Calendar className="h-4 w-4 flex-shrink-0" />
                <span>Joined {profile.joinDate.toLocaleDateString()}</span>
              </li>
            </ul>



            {/* Gaming Focus - Secondary styling */}
            {profile.stats.mostPlayedGenre && (
              <div>
                <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-2">Gaming Focus</h3>
                <div className="flex flex-wrap gap-1">
                  <Badge variant="secondary" className="text-xs px-2 py-0.5 font-normal capitalize">
                    {profile.stats.mostPlayedGenre}
                  </Badge>
                  <Badge variant="secondary" className="text-xs px-2 py-0.5 font-normal">
                    Active Player
                  </Badge>
                </div>
              </div>
            )}

            {/* Active Guilds - Key information */}
            {profile.guilds.filter(g => g.isActive).length > 0 && (
              <div>
                <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-2">Active Guilds</h3>
                <div className="flex flex-wrap gap-2">
                  {profile.guilds.filter(g => g.isActive).map((guild) => (
                    <div key={guild.guildId} className="flex items-center gap-2 bg-muted/30 rounded-lg px-3 py-2 text-sm">
                      <Users className="h-3 w-3 text-muted-foreground flex-shrink-0" />
                      <span className="font-medium">{guild.guildName}</span>
                      <Badge variant="outline" className="text-xs">
                        {guild.role}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Preferred Roles - Smaller, secondary styling */}
            {profile.preferredRoles.length > 0 && (
              <div>
                <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wide mb-2">Preferred Roles</h3>
                <div className="flex flex-wrap gap-1">
                  {profile.preferredRoles.map((role) => (
                    <Badge key={role} variant="secondary" className="text-xs px-2 py-0.5 font-normal">
                      {role}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
