import { render, screen } from '@testing-library/react';
import { GamingStats } from '../gaming-stats';
import { mockGamerProfile } from '@/lib/mock-data';

// Mock the icons
jest.mock('lucide-react', () => ({
  Clock: () => <div data-testid="clock-icon" />,
  Trophy: () => <div data-testid="trophy-icon" />,
  Users: () => <div data-testid="users-icon" />,
  Gamepad2: () => <div data-testid="gamepad-icon" />,
  Target: () => <div data-testid="target-icon" />,
  TrendingUp: () => <div data-testid="trending-up-icon" />,
  Star: () => <div data-testid="star-icon" />,
  Timer: () => <div data-testid="timer-icon" />,
}));

describe('GamingStats', () => {
  const stats = mockGamerProfile.stats;

  it('renders all stat cards', () => {
    render(<GamingStats stats={stats} />);
    
    expect(screen.getByText('Total Playtime')).toBeInTheDocument();
    expect(screen.getByText('Achievements')).toBeInTheDocument();
    expect(screen.getByText('Games Library')).toBeInTheDocument();
    expect(screen.getByText('Social')).toBeInTheDocument();
    expect(screen.getByText('Gaming Focus')).toBeInTheDocument();
    expect(screen.getByText('Session Stats')).toBeInTheDocument();
  });

  it('formats playtime correctly', () => {
    render(<GamingStats stats={stats} />);
    
    // 4097 hours should be formatted as days and hours
    const expectedDays = Math.floor(stats.totalHoursPlayed / 24);
    const expectedHours = stats.totalHoursPlayed % 24;
    expect(screen.getByText(`${expectedDays}d ${expectedHours}h`)).toBeInTheDocument();
  });

  it('displays achievement count and progress', () => {
    render(<GamingStats stats={stats} />);
    
    expect(screen.getByText(stats.achievementsUnlocked.toString())).toBeInTheDocument();
    expect(screen.getByText(/\d+% completion rate/)).toBeInTheDocument();
  });

  it('shows games library information', () => {
    render(<GamingStats stats={stats} />);
    
    expect(screen.getByText(stats.gamesOwned.toString())).toBeInTheDocument();
    if (stats.favoriteGame) {
      expect(screen.getByText('Favorite Game')).toBeInTheDocument();
      expect(screen.getByText(stats.favoriteGame)).toBeInTheDocument();
    }
  });

  it('displays social stats', () => {
    render(<GamingStats stats={stats} />);
    
    expect(screen.getByText('Friends')).toBeInTheDocument();
    expect(screen.getByText(stats.friendsCount.toString())).toBeInTheDocument();
    expect(screen.getByText('Guilds')).toBeInTheDocument();
    expect(screen.getByText(stats.guildsJoined.toString())).toBeInTheDocument();
  });

  it('shows gaming focus with most played genre', () => {
    render(<GamingStats stats={stats} />);
    
    if (stats.mostPlayedGenre) {
      expect(screen.getByText('Most Played Genre')).toBeInTheDocument();
      expect(screen.getByText(stats.mostPlayedGenre, { exact: false })).toBeInTheDocument();
    }
  });

  it('formats session length correctly', () => {
    render(<GamingStats stats={stats} />);
    
    // 180 minutes should be formatted as 3h 0m
    const hours = Math.floor(stats.averageSessionLength / 60);
    const minutes = stats.averageSessionLength % 60;
    expect(screen.getByText(`${hours}h ${minutes}m`)).toBeInTheDocument();
  });

  it('calculates total sessions correctly', () => {
    render(<GamingStats stats={stats} />);
    
    const expectedSessions = Math.round(stats.totalHoursPlayed * 60 / stats.averageSessionLength);
    expect(screen.getByText(expectedSessions.toString())).toBeInTheDocument();
  });

  it('displays correct playtime rank based on hours', () => {
    render(<GamingStats stats={stats} />);
    
    // With 4097 hours, should be "Master" rank
    expect(screen.getByText('Master')).toBeInTheDocument();
  });

  it('handles edge cases for formatting', () => {
    const lowStats = {
      ...stats,
      totalHoursPlayed: 12, // Less than 24 hours
      averageSessionLength: 45, // Less than 60 minutes
    };
    
    render(<GamingStats stats={lowStats} />);
    
    expect(screen.getByText('12h')).toBeInTheDocument(); // Should show hours only
    expect(screen.getByText('45m')).toBeInTheDocument(); // Should show minutes only
  });

  it('shows active player badge', () => {
    render(<GamingStats stats={stats} />);
    
    expect(screen.getByText('Active Player')).toBeInTheDocument();
  });
});
