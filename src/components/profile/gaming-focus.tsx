'use client';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { GamerStats } from '@/types/gamer';
import { 
  Target,
  TrendingUp
} from 'lucide-react';

interface GamingFocusProps {
  stats: GamerStats;
}

export function GamingFocus({ stats }: GamingFocusProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Gaming Focus</CardTitle>
        <Target className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {stats.mostPlayedGenre && (
          <div>
            <p className="text-xs text-muted-foreground">Most Played Genre</p>
            <p className="text-sm font-medium capitalize">{stats.mostPlayedGenre}</p>
          </div>
        )}
        <div className="mt-3">
          <Badge variant="secondary" className="text-xs">
            <TrendingUp className="h-3 w-3 mr-1" />
            Active Player
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
}
