export interface GamerProfile {
  id: string;
  username: string;
  displayName: string;
  avatar?: string;
  bio?: string;
  location?: string;
  timezone?: string;
  joinDate: Date;
  isOnline: boolean;
  lastSeen?: Date;
  
  // Gaming preferences
  playStyle: 'casual' | 'competitive' | 'mixed';
  preferredRoles: string[];
  availableHours: AvailableHours;
  
  // Games and characters
  games: GameProfile[];
  achievements: Achievement[];
  
  // Social features
  friends: string[]; // User IDs
  guilds: GuildMembership[];
  
  // Privacy settings
  privacy: PrivacySettings;
  
  // Statistics
  stats: GamerStats;
}

export interface GameProfile {
  gameId: string;
  gameName: string;
  gameIcon?: string;
  platform: Platform;
  server?: string;
  region?: string;
  
  // Character information
  characters: Character[];
  
  // Game-specific stats
  hoursPlayed: number;
  level?: number;
  rank?: string;
  achievements: GameAchievement[];
  
  // Timestamps
  startedPlaying: Date;
  lastPlayed: Date;
}

export interface Character {
  id: string;
  name: string;
  class?: string;
  race?: string;
  level: number;
  server?: string;
  faction?: string;
  specialization?: string;
  itemLevel?: number;
  avatar?: string;
  isPrimary: boolean;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon?: string;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  unlockedAt: Date;
  gameId?: string;
  progress?: number;
  maxProgress?: number;
}

export interface GameAchievement extends Achievement {
  gameSpecific: boolean;
  category: string;
}

export interface GuildMembership {
  guildId: string;
  guildName: string;
  guildTag?: string;
  role: 'member' | 'officer' | 'leader';
  joinedAt: Date;
  gameId: string;
  isActive: boolean;
}

export interface AvailableHours {
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  sunday: TimeSlot[];
}

export interface TimeSlot {
  start: string; // HH:MM format
  end: string;   // HH:MM format
}

export interface PrivacySettings {
  profileVisibility: 'public' | 'friends' | 'private';
  showOnlineStatus: boolean;
  showGameActivity: boolean;
  showAchievements: boolean;
  showStats: boolean;
  allowFriendRequests: boolean;
  allowGuildInvites: boolean;
}

export interface GamerStats {
  totalHoursPlayed: number;
  gamesOwned: number;
  achievementsUnlocked: number;
  guildsJoined: number;
  friendsCount: number;
  favoriteGame?: string;
  mostPlayedGenre?: string;
  averageSessionLength: number; // in minutes
}

export type Platform = 
  | 'pc'
  | 'playstation'
  | 'xbox'
  | 'nintendo'
  | 'mobile'
  | 'steam'
  | 'epic'
  | 'battlenet'
  | 'origin'
  | 'uplay';

export type GameGenre = 
  | 'mmorpg'
  | 'fps'
  | 'moba'
  | 'rts'
  | 'rpg'
  | 'action'
  | 'adventure'
  | 'simulation'
  | 'strategy'
  | 'sports'
  | 'racing'
  | 'puzzle'
  | 'indie';

// Form types for editing profile
export interface EditGamerProfileForm {
  displayName: string;
  bio: string;
  location: string;
  timezone: string;
  playStyle: GamerProfile['playStyle'];
  preferredRoles: string[];
  privacy: PrivacySettings;
}

export interface AddGameForm {
  gameId: string;
  gameName: string;
  platform: Platform;
  server?: string;
  region?: string;
  hoursPlayed: number;
  level?: number;
  rank?: string;
}

export interface AddCharacterForm {
  name: string;
  class?: string;
  race?: string;
  level: number;
  server?: string;
  faction?: string;
  specialization?: string;
  itemLevel?: number;
  isPrimary: boolean;
}
