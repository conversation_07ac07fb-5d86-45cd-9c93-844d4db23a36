# Gamer Profile Page Documentation

## Overview

The Gamer Profile Page is a comprehensive component system that displays detailed information about a gamer's profile, including their gaming statistics, character information, achievements, and social connections. Built with Next.js, TypeScript, shadcn/ui, and Tailwind CSS.

## Features

### 🎮 Core Features
- **Profile Header**: Display name, avatar, bio, location, and online status
- **Gaming Statistics**: Playtime, achievements, games library, and session stats
- **Games & Characters**: Multi-game support with character management
- **Achievements System**: Rarity-based achievements with progress tracking
- **Social Features**: Guild memberships and friend connections
- **Edit Mode**: Profile editing for authenticated users

### 🎨 Design Features
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Dark Mode Support**: Full dark/light theme compatibility
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **Loading States**: Skeleton loaders and smooth transitions
- **Error Handling**: Graceful error states with retry functionality

## File Structure

```
src/
├── app/
│   ├── profile/
│   │   └── [username]/
│   │       └── page.tsx          # Main profile page
│   └── page.tsx                  # Homepage with demo link
├── components/
│   ├── profile/
│   │   ├── profile-header.tsx    # Profile header component
│   │   ├── gaming-stats.tsx      # Gaming statistics display
│   │   ├── games-characters.tsx  # Games and characters management
│   │   ├── achievements.tsx      # Achievements display
│   │   ├── profile-skeleton.tsx  # Loading skeletons
│   │   └── __tests__/           # Component tests
│   └── ui/                      # shadcn/ui components
├── contexts/
│   └── profile-context.tsx      # Profile state management
├── types/
│   └── gamer.ts                 # TypeScript interfaces
├── lib/
│   ├── mock-data.ts             # Mock data for development
│   └── utils.ts                 # Utility functions
└── docs/
    └── PROFILE_PAGE.md          # This documentation
```

## Components

### ProfileHeader
**Location**: `src/components/profile/profile-header.tsx`

Displays the main profile information including avatar, name, bio, and key statistics.

**Props**:
- `profile: GamerProfile` - The user's profile data
- `isOwnProfile?: boolean` - Whether this is the current user's profile
- `onProfileUpdate?: (updates: Partial<GamerProfile>) => void` - Callback for profile updates

**Features**:
- Online/offline status indicator
- Editable profile information (for own profile)
- Play style and preferred roles display
- Responsive avatar with fallback
- Accessibility support with ARIA labels

### GamingStats
**Location**: `src/components/profile/gaming-stats.tsx`

Shows comprehensive gaming statistics in a card-based layout.

**Props**:
- `stats: GamerStats` - Gaming statistics object

**Features**:
- Total playtime with rank system
- Achievement progress tracking
- Games library overview
- Social statistics
- Session information
- Responsive grid layout

### GamesCharacters
**Location**: `src/components/profile/games-characters.tsx`

Manages display of games and associated characters.

**Props**:
- `games: GameProfile[]` - Array of game profiles
- `isOwnProfile?: boolean` - Whether user can edit

**Features**:
- Tabbed interface (Overview/Characters)
- Game-specific character management
- Platform badges and server information
- Character level and class display
- Add game/character functionality (for own profile)

### Achievements
**Location**: `src/components/profile/achievements.tsx`

Displays achievements with filtering and categorization.

**Props**:
- `achievements: Achievement[]` - User's achievements
- `recentAchievements?: Achievement[]` - Recent achievements

**Features**:
- Rarity-based filtering (All/Recent/Rare+)
- Achievement progress bars
- Game-specific achievement categorization
- Rarity statistics overview
- Recent achievements highlight

## State Management

### ProfileContext
**Location**: `src/contexts/profile-context.tsx`

Provides centralized state management for profile data using React's useReducer.

**State**:
```typescript
interface ProfileState {
  profile: GamerProfile | null;
  recentAchievements: Achievement[];
  loading: boolean;
  error: string | null;
  isEditing: boolean;
}
```

**Actions**:
- `setLoading(loading: boolean)` - Set loading state
- `setProfile(profile: GamerProfile)` - Set profile data
- `setRecentAchievements(achievements: Achievement[])` - Set recent achievements
- `setError(error: string | null)` - Set error state
- `updateProfile(updates: Partial<GamerProfile>)` - Update profile data
- `addAchievement(achievement: Achievement)` - Add new achievement

## Data Types

### Core Types
**Location**: `src/types/gamer.ts`

Key interfaces:
- `GamerProfile` - Main profile structure
- `GameProfile` - Game-specific data
- `Character` - Character information
- `Achievement` - Achievement data
- `GamerStats` - Statistics object
- `PrivacySettings` - Privacy configuration

## API Integration

### Mock Data
**Location**: `src/lib/mock-data.ts`

Provides mock data and simulated API functions for development:
- `fetchGamerProfile(userId: string)` - Fetch profile data
- `updateGamerProfile(userId: string, updates: Partial<GamerProfile>)` - Update profile
- `fetchRecentAchievements(userId: string)` - Fetch recent achievements

## Styling

### Tailwind CSS Classes
The components use a consistent design system with:
- **Colors**: Primary, secondary, muted, destructive
- **Spacing**: Consistent padding and margins
- **Typography**: Hierarchical text sizing
- **Responsive**: Mobile-first breakpoints (sm, md, lg)

### Dark Mode
Full dark mode support using CSS variables and Tailwind's dark: prefix.

## Accessibility

### Features Implemented
- **ARIA Labels**: Descriptive labels for interactive elements
- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Reader Support**: Proper heading hierarchy and descriptions
- **Focus Management**: Visible focus indicators and logical tab order
- **Color Contrast**: WCAG compliant color combinations

### Testing
Accessibility tested with:
- Screen reader compatibility
- Keyboard-only navigation
- Color contrast validation
- Focus management verification

## Testing

### Test Files
- `src/components/profile/__tests__/profile-header.test.tsx`
- `src/components/profile/__tests__/gaming-stats.test.tsx`

### Test Coverage
- Component rendering
- User interactions
- Accessibility features
- Data formatting
- Error states
- Loading states

### Running Tests
```bash
npm test                    # Run all tests
npm test -- --watch       # Run tests in watch mode
npm test -- --coverage    # Run tests with coverage
```

## Usage Examples

### Basic Profile Display
```tsx
import { ProfileHeader } from '@/components/profile/profile-header';
import { mockGamerProfile } from '@/lib/mock-data';

function MyComponent() {
  return (
    <ProfileHeader 
      profile={mockGamerProfile}
      isOwnProfile={false}
    />
  );
}
```

### With State Management
```tsx
import { ProfileProvider, useProfile } from '@/contexts/profile-context';

function ProfilePage() {
  return (
    <ProfileProvider>
      <ProfileContent />
    </ProfileProvider>
  );
}

function ProfileContent() {
  const { state, actions } = useProfile();
  
  if (state.loading) return <ProfileSkeleton />;
  if (state.error) return <ErrorDisplay />;
  
  return <ProfileHeader profile={state.profile} />;
}
```

## Performance Considerations

### Optimizations
- **Lazy Loading**: Components load on demand
- **Memoization**: Expensive calculations cached
- **Skeleton Loading**: Immediate visual feedback
- **Error Boundaries**: Graceful error handling

### Bundle Size
- Tree-shaking enabled for unused components
- Dynamic imports for heavy components
- Optimized icon usage with Lucide React

## Future Enhancements

### Planned Features
- Real-time updates via WebSocket
- Advanced filtering and search
- Profile comparison tools
- Achievement sharing
- Guild integration
- Mobile app support

### Technical Improvements
- Server-side rendering optimization
- Progressive Web App features
- Advanced caching strategies
- Performance monitoring
- A/B testing framework

## Contributing

### Development Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Run development server: `npm run dev`
4. Run tests: `npm test`

### Code Standards
- TypeScript strict mode
- ESLint configuration
- Prettier formatting
- Component testing required
- Accessibility compliance

### Pull Request Process
1. Create feature branch
2. Implement changes with tests
3. Update documentation
4. Submit PR with description
5. Code review and approval
