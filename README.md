# Guildie - Gaming Community Platform

A modern web application that allows gamers to connect with one another by finding guilds for specific games, showcasing their gaming achievements, and building their gaming network.

## 🎮 Features

### Core Functionality
- **Gamer Profiles**: Comprehensive profiles with gaming statistics, achievements, and character information
- **Multi-Game Support**: Track progress across multiple games with character management
- **Achievement System**: Rarity-based achievements with progress tracking
- **Guild Integration**: Display guild memberships and roles
- **Social Features**: Friend connections and gaming network

### Technical Features
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Dark Mode Support**: Full dark/light theme compatibility
- **Accessibility**: ARIA labels, keyboard navigation, screen reader support
- **Loading States**: Skeleton loaders and smooth transitions
- **Error Handling**: Graceful error states with retry functionality
- **Type Safety**: Full TypeScript implementation

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd guildie
```

2. Install dependencies:
```bash
npm install
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

### Demo
Visit the demo profile at `/profile/shadowhunter92` to see the full functionality.

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: shadcn/ui
- **Icons**: Lucide React
- **Testing**: Jest + React Testing Library
- **State Management**: React Context + useReducer

## 📁 Project Structure

```
src/
├── app/                     # Next.js App Router
│   ├── profile/[username]/  # Dynamic profile pages
│   └── page.tsx            # Homepage
├── components/
│   ├── profile/            # Profile-specific components
│   └── ui/                 # shadcn/ui components
├── contexts/               # React Context providers
├── types/                  # TypeScript type definitions
├── lib/                    # Utilities and mock data
└── docs/                   # Documentation
```

## 🧪 Testing

Run the test suite:
```bash
npm test                    # Run all tests
npm run test:watch         # Run tests in watch mode
npm run test:coverage      # Run tests with coverage
```

## 📖 Documentation

Detailed documentation is available in the `/docs` folder:
- [Profile Page Documentation](./docs/PROFILE_PAGE.md) - Comprehensive guide to the profile system

## 🎨 Design System

The application uses a consistent design system built with:
- **shadcn/ui**: High-quality, accessible components
- **Tailwind CSS**: Utility-first styling with custom design tokens
- **Responsive Design**: Mobile-first approach with breakpoints
- **Dark Mode**: CSS variables for seamless theme switching

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm test` - Run tests

### Code Standards
- TypeScript strict mode enabled
- ESLint + Prettier for code formatting
- Component testing with React Testing Library
- Accessibility compliance (WCAG guidelines)

## 🚀 Deployment

The application is ready for deployment on platforms like:
- Vercel (recommended)
- Netlify
- AWS Amplify
- Any Node.js hosting platform

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Built with [Next.js](https://nextjs.org/)
- UI components from [shadcn/ui](https://ui.shadcn.com/)
- Icons from [Lucide](https://lucide.dev/)
- Styled with [Tailwind CSS](https://tailwindcss.com/)
